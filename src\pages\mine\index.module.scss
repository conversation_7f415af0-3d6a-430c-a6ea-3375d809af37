

.topHeader {
  box-sizing: content-box;
  height: 184px;
  width: 100%;
  background-color: $primary-color;
  clip-path: polygon(0 0, 100% 0, 100% 88%, 50% 100%, 0 88%);
  z-index: -1;
}

.body {
  position: relative;
  padding: 32px 40px;
  width: 100%;
  margin-top: -160px;

}

.content {
  padding: 32px;
  background-color: #fff;
  border-radius: 24px;
  margin-bottom: 24px;
}

.option {
  display: flex;
  justify-content: space-between;
  border-bottom: 1rpx solid #e9edf3ff;
  padding: 33px 0;
  font-size: 28px;
  color: rgba(0, 0, 0, 0.85);
  cursor: pointer;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0px;
  }

  &:first-child {
    padding-top: 0px;
  }
}

.userInfo {
  display: flex;
  align-items: center;
  border-style: none;
  background-color: transparent;
  box-shadow: none;
}

.userName {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  font-size: 46px;
  line-height: 64px;
}

.userImg {
  width: 112px;
  height: 112px;
  border-radius: 12px;
}

.loginBox {
  margin-left: 24px;
}

.loginDesc {
  margin-top: 6px;
  color: #00000073;
  font-weight: 400;
  font-size: 28px;
  line-height: 150%;
}

.row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.iconfont {}
