import { View, Text, Image as Img, ScrollView } from '@tarojs/components'
import { useRef, useState } from 'react'
import * as cn from 'classnames'
import { useDidShow, useLoad } from '@tarojs/taro'
import s from './index.module.scss'
import CerRecruitCard from '../components/CerRecruitCard'
import Header from '@/components/Header'
import More from '../components/More'
import useAuthCode from '@/hooks/useAuthCode'
import GuideLoginPop from '@/components/GuideLoginPop'
import GenerateResumePublish from '@/pages/recruit/components/GenerateResumePublish'
import Page from '@/components/Page'
import Empty from '@/components/Empty'
import { generateHopeOcc } from '@/pages/index/components/List'
import { store } from '@/core/store'

export default function Index() {
  const [authCode] = useAuthCode()
  const [recruitList, setRecruitList] = useState<any>([])
  const [menuList, setMenuList] = useState([{ label: '优质职位离家近', active: true }, { label: '全部职位', active: false }])
  const currentChildRef = useRef<{ clickFreeSighUp?: Function }>({})
  const [signedJobs, setSignedJobs] = useState<Record<number, boolean>>({})
  const [releaseResumeJob, setReleaseResumeJobShow] = useState(false)
  const [currentCardInfo, setCurrentCardInfo] = useState<null | any>({})
  const [guideLoginPosition, setPosition] = useState($.router.query.guideLoginPosition)

  const rqInitList = async () => {
    const q = $.taro.getLaunchOptionsSync()
    const { launchQuery } = store.getState().storage
    const [, res] = await $.request['POST/job/v3/list/customized/douyinMini/leadGenerationConfigJobsWithRouteable']({
      routeId: launchQuery.routeId || q.query.routeId || '',
    }).catch((err) => {
      if (err?.[1]?.code != '401') {
        setRecruitList([])
      }
    })
    // const [, res] = await $.request['POST/job/v3/list/customized/douyinMini/leadGenerationConfigJobs']().catch((err) => {
    //   if (err?.[1]?.code != '401') {
    //     setRecruitList([])
    //   }
    // })
    if (res?.data?.jobs?.length > 0) {
      setRecruitList(res.data.jobs)
    } else {
      setRecruitList([])
    }
  }

  useLoad(() => {
    if ($.router.query.guideLoginPosition) {
      setPosition($.router.query.guideLoginPosition)
    }
    rqInitList()
  })

  const onclickMenu = (index: number) => {
    setMenuList(menuList.map((item, i) => {
      if (i === index) {
        return { ...item, active: true }
      }
      return { ...item, active: false }
    }))
    if (index === 1) {
      // 进入找活干页面
      $.router.push('/pages/index/index')
    } else {
      rqInitList()
    }
  }

  // 创建报名成功回调
  const handleSignSuccess = (jobId: number) => {
    setSignedJobs(prev => ({ ...prev, [jobId]: true }))
  }

  const applyCallBack = () => {
    setReleaseResumeJobShow(false)
    if (currentChildRef.current?.clickFreeSighUp && currentCardInfo?.basicInfo?.jobId) {
      currentChildRef.current.clickFreeSighUp(currentCardInfo)
    }
  }

  const emptyOptions = { title: '该区域无数据', desc: '请扩大搜索范围看看呢~', img: 'https://cdn.yupaowang.com/yupao_common/580d952d.png' }

  return (
    <Page>
      <View className={s.page}>
        <View id="header">
          <Header>
            <Img className={s.HeaderImg} src='https://cdn.yupaowang.com/yupao_mini/yp-mini_tt_sinatv_head.png' />
          </Header>
        </View>
        <View className={s.container}>
          <View className={s.menuContainer}>
            {menuList.map((item, index) => (
              <View className={cn(s.menuItem, item.active && s.activeItem)} key={index} onClick={() => onclickMenu(index)}>
                {item.label}
              </View>
            ))}

          </View>
          <View className={s.cardContainer}>
            <View className={s.titleBox}>
              <Img className={s.titleImg} src="https://cdn.yupaowang.com/yupao_common/0e60e920.png" mode="aspectFill"></Img>
              <Text className={s.titleText}>优质职位 马上去报名</Text>
            </View>
            <ScrollView className={s.scrollList} scrollY style={{ height: 'calc(100% - 40px)' }}>
              {recruitList.map((item, index) => (
                <View className={s.cardBox} key={index}>
                  <CerRecruitCard
                    ref={currentChildRef}
                    i={index}
                    cardInfo={item}
                    page={item.currentPage}
                    authCode={authCode}
                    signed={signedJobs[item.basicInfo.jobId]}
                    onSignSuccess={handleSignSuccess} // 新增成功回调
                    onRefreshList={() => rqInitList()}
                    onOpenResumePublish={() => {
                      setReleaseResumeJobShow(true)
                      setCurrentCardInfo(item)
                    }}
                  />
                </View>
              ))}
              {recruitList.length > 0 && <More />}
              {recruitList.length === 0 && <Empty {...emptyOptions} />}
            </ScrollView>
          </View>
        </View>
        <GenerateResumePublish
          sourceId='1'
          visible={releaseResumeJob}
          hopeAreas={currentCardInfo?.address?.provCityDist?.cityId || []}
          occupations={generateHopeOcc(currentCardInfo?.basicInfo?.occupations) || []}
          onClose={() => setReleaseResumeJobShow(false)}
          onConfirm={applyCallBack}
        />
      </View>
      <GuideLoginPop position={guideLoginPosition} onLoginSuccess={() => rqInitList()} />
    </Page>
  )
}
