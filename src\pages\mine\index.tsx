/*
 * @Date: 2024-11-21 16:57:14
 * @Description: 我的页面
 */

import React, { useEffect, useState } from 'react'
import { useDidShow } from '@tarojs/taro'
import { Button } from '@tarojs/components'
import Page from '@/components/Page'
import s from './index.module.scss'
import { actions, dispatch, store, useSelector } from '@/core/store'
import IconFont from '@/components/IconFont'
import { useLogin } from '@/core/utils/login'
import LoginBtn from '@/components/LoginBtn'

// 定义选项配置类型
type Option = {
  title: string;
  isLink?: boolean;
  actionKey: string;
};

// 定义用户数据类型
type User = {
  isLogin: boolean; // 是否登录
  name?: string; // 用户名（未登录时为空）
  avatar?: string; // 用户头像
};

// 用户信息组件
const UserInfo: React.FC<{
  user: User;
}> = ({ user }) => {
  const [loginProps] = useLogin({ })

  return (
    <LoginBtn className={s.userInfo} {...loginProps}>
      <Img
        className={s.userImg}
        src={
          user.avatar || 'https://cdn.yupaowang.com/yupao_mini/unloginAvatar.png'
        }
      />
      {user.isLogin ? (
        <V className={s.loginBox}>
          <T className={s.userName}>{user.name}</T>
        </V>
      ) : (
        <V className={s.loginBox}>
          <V className={s.userName} >
          登录/注册
          </V>
          <V className={s.loginDesc}>登录体验更多服务</V>
        </V>
      )}
    </LoginBtn>
  )
}

const MyPage: React.FC = () => {
  // 用户数据状态
  const [user, setUser] = useState<User>({
    isLogin: false,
    name: '',
    avatar: '',
  })

  const userInfo = useSelector(state => state.storage.userInfo) as any

  const isLogin = !!useSelector(state => state.storage.token)

  const sysInfo = $.sysInfo()

  const [loginProps] = useLogin({ })

  // 选项配置
  const [options, setOptions] = useState<Option[]>(() => {
    const arr = [
      { title: '意见反馈', isLink: true, actionKey: 'feedback' },
      { title: '资质公示', isLink: true, actionKey: 'qualification' },
    ]
    if (process.env.TARO_APP_ENV !== 'prod') {
      arr.unshift({ title: '开发环境', isLink: true, actionKey: 'dev' })
      arr.push({ title: '清除所有缓存', isLink: false, actionKey: 'clearAllCache' })
    }
    return arr
  })

  // 登出逻辑
  const handleLogout = () => {
    dispatch(actions.user.clearCache())
    $.msg('退出登录成功')
  }

  // 清除所有缓存
  const handleClearAllCache = () => {
    $.confirm({
      title: '温馨提示',
      content: '确定要清除所有缓存吗？包括storage、登录缓存和所有数据缓存',
      cancelText: '取消',
      confirmText: '确认',
    }).then(() => {
      // 调用快手小程序API清除本地数据缓存
      try {
        ks.clearStorageSync()
      } catch (error) {
        console.error('清除本地缓存失败:', error)
      }

      // 清除storage缓存
      dispatch(actions.storage.clear())
      // 清除用户缓存
      dispatch(actions.user.clearCache())
      // 清除其他store数据
      dispatch(actions.global.setState({}))
      dispatch(actions.classify.setState({}))
      dispatch(actions.address.setState({}))
      dispatch(actions.resume.setState({}))
      dispatch(actions.recruit.setState({}))
      dispatch(actions.user.setState({}))

      $.msg('清除所有缓存成功')
      // 刷新页面
      setTimeout(() => {
        $.router.reLaunch('/pages/mine/index')
      }, 1000)
    })
  }

  // 统一处理点击事件
  const handleOptionClick = async (actionKey: string) => {
    switch (actionKey) {
    case 'dev':
      $.router.push('/subpackage/dev/index/index')
      break
    case 'feedback':
      if (!user.isLogin) {
        await $.login()
      }
      $.router.push('/subpackage/web-view/index', {
        url: '/advice-feedback/add',
        // isLogin: true,
      })
      break
    case 'qualification':
      $.router.push('/subpackage/certification/index')
      break
    case 'clearAllCache':
      handleClearAllCache()
      break
    case 'logout':
      $.confirm({
        title: '温馨提示',
        content: '是否退出登录？',
        cancelText: '取消',
        confirmText: '确认',
      }).then(() => {
        handleLogout()
      })
      break
    default:
      console.log('未定义的操作')
    }
  }

  useEffect(() => {
    if (isLogin) {
      setUser({
        isLogin: true,
        name: userInfo.userName,
        avatar: userInfo.headPortrait,
      })
      setOptions(prev => {
        // 是否已包含logout
        const isHaveLogout = prev.some(item => item.actionKey === 'logout')
        return isHaveLogout ? prev : [...prev, { title: '退出', isLink: false, actionKey: 'logout' }]
      })
    } else {
      setUser({
        isLogin: false,
        name: '',
        avatar: '',
      })
      setOptions(prev => prev.filter(item => item.actionKey !== 'logout'))
    }
  }, [isLogin, userInfo])

  useDidShow(() => {
    store.dispatch(actions.storage.refreshUserInfo())
  })

  return (
    <Page>
      <V className={s.topHeader} style={{ paddingTop: sysInfo.statusBarHeight + sysInfo.menuRect.height }}></V>
      <V className={s.body}>
        {/* 用户模块 */}
        <V className={s.content}>
          <UserInfo user={user} />
        </V>
        {/* 配置化选项 */}
        <V className={s.content}>
          {options.map((option) => (
            <V key={option.actionKey}>
              {option.actionKey == 'feedBack'
                ? <Button
                  className={s.option}
                  {...loginProps}
                >
                  <T>{option.title}</T>
                  {
                    option.isLink && <IconFont className={s.iconfont} size={32} type='yp-mianbaoxue' />
                  }
                </Button>
                : <V
                  className={s.option}
                  onClick={() => handleOptionClick(option.actionKey)}
                >
                  <T>{option.title}</T>
                  {
                    option.isLink && <IconFont className={s.iconfont} size={32} type='yp-mianbaoxue' />
                  }
                </V>
              }
            </V>

          ))}

        </V>
      </V>
    </Page>
  )
}

export default MyPage
